{"name": "app-review-today", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run generate-seo && vite build", "lint": "eslint .", "preview": "vite preview", "generate-seo": "node scripts/generate-sitemap.cjs"}, "dependencies": {"@stripe/stripe-js": "^7.6.1", "@supabase/supabase-js": "^2.39.3", "@tanstack/react-query": "^5.17.15", "@vercel/analytics": "^1.5.0", "framer-motion": "^10.18.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-markdown": "^10.1.0", "react-router-dom": "^6.21.3", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "stripe": "^18.3.0", "web-vitals": "^5.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "gh-pages": "^6.1.1", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}