// Mobile optimization utilities and checks

export interface MobileOptimizationReport {
  touchTargets: {
    passed: boolean
    issues: string[]
  }
  viewport: {
    passed: boolean
    issues: string[]
  }
  performance: {
    passed: boolean
    issues: string[]
  }
  accessibility: {
    passed: boolean
    issues: string[]
  }
  overall: {
    score: number
    grade: 'A' | 'B' | 'C' | 'D' | 'F'
  }
}

// Check touch target sizes (minimum 44px x 44px recommended)
export const checkTouchTargets = (): { passed: boolean; issues: string[] } => {
  const issues: string[] = []
  const minTouchSize = 44 // pixels

  // Get all interactive elements
  const interactiveSelectors = [
    'button',
    'a',
    'input[type="button"]',
    'input[type="submit"]',
    'input[type="reset"]',
    '[role="button"]',
    '[onclick]',
    'select',
    'input[type="checkbox"]',
    'input[type="radio"]'
  ]

  interactiveSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    elements.forEach((element, index) => {
      const rect = element.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(element)
      
      // Skip hidden elements
      if (rect.width === 0 || rect.height === 0 || computedStyle.display === 'none') {
        return
      }

      if (rect.width < minTouchSize || rect.height < minTouchSize) {
        issues.push(
          `${selector}[${index}]: Touch target too small (${Math.round(rect.width)}x${Math.round(rect.height)}px). Minimum recommended: ${minTouchSize}x${minTouchSize}px`
        )
      }
    })
  })

  return {
    passed: issues.length === 0,
    issues
  }
}

// Check viewport configuration
export const checkViewport = (): { passed: boolean; issues: string[] } => {
  const issues: string[] = []
  
  // Check for viewport meta tag
  const viewportMeta = document.querySelector('meta[name="viewport"]')
  if (!viewportMeta) {
    issues.push('Missing viewport meta tag')
  } else {
    const content = viewportMeta.getAttribute('content') || ''
    
    // Check for essential viewport properties
    if (!content.includes('width=device-width')) {
      issues.push('Viewport should include width=device-width')
    }
    
    if (!content.includes('initial-scale=1')) {
      issues.push('Viewport should include initial-scale=1')
    }
    
    // Check for problematic settings
    if (content.includes('user-scalable=no')) {
      issues.push('user-scalable=no prevents users from zooming (accessibility issue)')
    }
    
    if (content.includes('maximum-scale=1')) {
      issues.push('maximum-scale=1 prevents users from zooming (accessibility issue)')
    }
  }

  return {
    passed: issues.length === 0,
    issues
  }
}

// Check mobile performance indicators
export const checkMobilePerformance = (): { passed: boolean; issues: string[] } => {
  const issues: string[] = []

  // Check for large images
  const images = document.querySelectorAll('img')
  images.forEach((img, index) => {
    const rect = img.getBoundingClientRect()
    const naturalWidth = (img as HTMLImageElement).naturalWidth
    const naturalHeight = (img as HTMLImageElement).naturalHeight
    
    // Check if image is much larger than display size
    if (naturalWidth > rect.width * 2 || naturalHeight > rect.height * 2) {
      issues.push(`Image[${index}]: Oversized image (${naturalWidth}x${naturalHeight} displayed at ${Math.round(rect.width)}x${Math.round(rect.height)})`)
    }
  })

  // Check for missing lazy loading
  const lazyImages = document.querySelectorAll('img[loading="lazy"]')
  const totalImages = images.length
  const lazyPercentage = (lazyImages.length / totalImages) * 100

  if (totalImages > 3 && lazyPercentage < 50) {
    issues.push(`Only ${Math.round(lazyPercentage)}% of images use lazy loading. Consider adding loading="lazy" to improve performance.`)
  }

  // Check for excessive DOM size
  const allElements = document.querySelectorAll('*')
  if (allElements.length > 1500) {
    issues.push(`Large DOM size (${allElements.length} elements). Consider reducing complexity for better mobile performance.`)
  }

  return {
    passed: issues.length === 0,
    issues
  }
}

// Check mobile accessibility
export const checkMobileAccessibility = (): { passed: boolean; issues: string[] } => {
  const issues: string[] = []

  // Check for missing alt text on images
  const images = document.querySelectorAll('img')
  images.forEach((img, index) => {
    if (!img.getAttribute('alt')) {
      issues.push(`Image[${index}]: Missing alt text`)
    }
  })

  // Check for proper heading hierarchy
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
  let previousLevel = 0
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1))
    if (index === 0 && level !== 1) {
      issues.push('Page should start with h1 heading')
    }
    if (level > previousLevel + 1) {
      issues.push(`Heading level skip: ${heading.tagName} follows h${previousLevel}`)
    }
    previousLevel = level
  })

  // Check for proper focus indicators
  const focusableElements = document.querySelectorAll(
    'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
  )
  
  // This is a basic check - in a real implementation, you'd test focus visibility
  if (focusableElements.length > 0) {
    const style = document.createElement('style')
    style.textContent = `
      *:focus { outline: 2px solid blue !important; }
    `
    document.head.appendChild(style)
    
    // Remove the test style after a brief moment
    setTimeout(() => {
      document.head.removeChild(style)
    }, 100)
  }

  return {
    passed: issues.length === 0,
    issues
  }
}

// Run comprehensive mobile optimization check
export const runMobileOptimizationCheck = (): MobileOptimizationReport => {
  const touchTargets = checkTouchTargets()
  const viewport = checkViewport()
  const performance = checkMobilePerformance()
  const accessibility = checkMobileAccessibility()

  // Calculate overall score
  const checks = [touchTargets, viewport, performance, accessibility]
  const passedChecks = checks.filter(check => check.passed).length
  const score = (passedChecks / checks.length) * 100

  // Determine grade
  let grade: 'A' | 'B' | 'C' | 'D' | 'F'
  if (score >= 90) grade = 'A'
  else if (score >= 80) grade = 'B'
  else if (score >= 70) grade = 'C'
  else if (score >= 60) grade = 'D'
  else grade = 'F'

  return {
    touchTargets,
    viewport,
    performance,
    accessibility,
    overall: {
      score: Math.round(score),
      grade
    }
  }
}

// Mobile-specific utilities
export const mobileUtils = {
  // Check if device is mobile
  isMobile: (): boolean => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  },

  // Check if device is tablet
  isTablet: (): boolean => {
    return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent)
  },

  // Get device pixel ratio
  getDevicePixelRatio: (): number => {
    return window.devicePixelRatio || 1
  },

  // Check if device supports touch
  isTouchDevice: (): boolean => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  },

  // Get viewport dimensions
  getViewportSize: () => {
    return {
      width: Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0),
      height: Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0)
    }
  },

  // Check if in landscape mode
  isLandscape: (): boolean => {
    return window.innerWidth > window.innerHeight
  },

  // Add mobile-specific CSS classes
  addMobileClasses: () => {
    const html = document.documentElement
    
    if (mobileUtils.isMobile()) {
      html.classList.add('is-mobile')
    }
    
    if (mobileUtils.isTablet()) {
      html.classList.add('is-tablet')
    }
    
    if (mobileUtils.isTouchDevice()) {
      html.classList.add('is-touch')
    }
    
    if (mobileUtils.isLandscape()) {
      html.classList.add('is-landscape')
    } else {
      html.classList.add('is-portrait')
    }
  }
}

// Initialize mobile optimizations
export const initializeMobileOptimizations = () => {
  // Add mobile classes
  mobileUtils.addMobileClasses()

  // Update classes on orientation change
  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      mobileUtils.addMobileClasses()
    }, 100)
  })

  // Run optimization check in development
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      const report = runMobileOptimizationCheck()

      if (report.overall.grade !== 'A') {
        console.warn(`Mobile optimization grade: ${report.overall.grade} (${report.overall.score}%)`)

        // Log only critical issues (reduce noise from touch target warnings)
        Object.entries(report).forEach(([category, result]) => {
          if (category !== 'overall' && !result.passed && category !== 'touchTargets') {
            console.warn(`${category} issues:`, result.issues)
          }
        })

        // For touch targets, only show count instead of all details
        if (!report.touchTargets.passed) {
          console.warn(`Touch targets: ${report.touchTargets.issues.length} elements need larger touch targets (44x44px minimum)`)
        }
      }
    }, 2000) // Wait for page to fully load
  }
}
