import { useEffect } from 'react'

interface WebVitalsMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
}

// Core Web Vitals thresholds
const THRESHOLDS = {
  LCP: { good: 2500, poor: 4000 },
  FID: { good: 100, poor: 300 },
  CLS: { good: 0.1, poor: 0.25 },
  FCP: { good: 1800, poor: 3000 },
  TTFB: { good: 800, poor: 1800 }
}

const getRating = (name: string, value: number): 'good' | 'needs-improvement' | 'poor' => {
  const threshold = THRESHOLDS[name as keyof typeof THRESHOLDS]
  if (!threshold) return 'good'
  
  if (value <= threshold.good) return 'good'
  if (value <= threshold.poor) return 'needs-improvement'
  return 'poor'
}

const sendToAnalytics = (metric: WebVitalsMetric) => {
  // Send to Vercel Analytics
  if (typeof window !== 'undefined' && window.va) {
    window.va('track', 'Web Vitals', {
      metric_name: metric.name,
      metric_value: metric.value,
      metric_rating: metric.rating,
      metric_delta: metric.delta,
      metric_id: metric.id
    })
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${metric.name}:`, {
      value: metric.value,
      rating: metric.rating,
      delta: metric.delta
    })
  }
}

export const PerformanceMonitor: React.FC = () => {
  useEffect(() => {
    // Dynamic import of web-vitals to avoid blocking
    const loadWebVitals = async () => {
      try {
        const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals')
        
        // Measure Core Web Vitals
        getCLS((metric) => {
          sendToAnalytics({
            name: 'CLS',
            value: metric.value,
            rating: getRating('CLS', metric.value),
            delta: metric.delta,
            id: metric.id
          })
        })

        getFID((metric) => {
          sendToAnalytics({
            name: 'FID',
            value: metric.value,
            rating: getRating('FID', metric.value),
            delta: metric.delta,
            id: metric.id
          })
        })

        getFCP((metric) => {
          sendToAnalytics({
            name: 'FCP',
            value: metric.value,
            rating: getRating('FCP', metric.value),
            delta: metric.delta,
            id: metric.id
          })
        })

        getLCP((metric) => {
          sendToAnalytics({
            name: 'LCP',
            value: metric.value,
            rating: getRating('LCP', metric.value),
            delta: metric.delta,
            id: metric.id
          })
        })

        getTTFB((metric) => {
          sendToAnalytics({
            name: 'TTFB',
            value: metric.value,
            rating: getRating('TTFB', metric.value),
            delta: metric.delta,
            id: metric.id
          })
        })
      } catch (error) {
        console.warn('Failed to load web-vitals:', error)
      }
    }

    // Load web-vitals after the page has loaded
    if (document.readyState === 'complete') {
      loadWebVitals()
    } else {
      window.addEventListener('load', loadWebVitals)
      return () => window.removeEventListener('load', loadWebVitals)
    }
  }, [])

  // Monitor additional performance metrics
  useEffect(() => {
    const measureCustomMetrics = () => {
      if ('performance' in window && 'getEntriesByType' in performance) {
        // Measure navigation timing
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navigation) {
          const metrics = {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            firstByte: navigation.responseStart - navigation.requestStart,
            domInteractive: navigation.domInteractive - navigation.navigationStart
          }

          if (process.env.NODE_ENV === 'development') {
            console.log('[Performance] Navigation Metrics:', metrics)
          }
        }

        // Measure resource timing
        const resources = performance.getEntriesByType('resource')
        const slowResources = resources.filter(resource => resource.duration > 1000)
        
        if (slowResources.length > 0 && process.env.NODE_ENV === 'development') {
          console.warn('[Performance] Slow resources detected:', slowResources)
        }
      }
    }

    // Measure after page load
    if (document.readyState === 'complete') {
      measureCustomMetrics()
    } else {
      window.addEventListener('load', measureCustomMetrics)
      return () => window.removeEventListener('load', measureCustomMetrics)
    }
  }, [])

  return null // This component doesn't render anything
}

// Utility function to preload critical resources
export const preloadCriticalResources = () => {
  // Preload critical fonts
  const fontPreloads = [
    '/fonts/inter-var.woff2',
    '/fonts/inter-var-latin.woff2'
  ]

  fontPreloads.forEach(href => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.type = 'font/woff2'
    link.crossOrigin = 'anonymous'
    link.href = href
    document.head.appendChild(link)
  })

  // Preload critical images
  const criticalImages = [
    '/app-review-today.svg',
    '/app-review-today-192.png'
  ]

  criticalImages.forEach(src => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = src
    document.head.appendChild(link)
  })
}

// Performance optimization utilities
export const performanceUtils = {
  // Debounce function for performance-sensitive operations
  debounce: <T extends (...args: any[]) => any>(func: T, wait: number) => {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  },

  // Throttle function for scroll/resize events
  throttle: <T extends (...args: any[]) => any>(func: T, limit: number) => {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // Check if user prefers reduced motion
  prefersReducedMotion: () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  },

  // Check connection quality
  getConnectionQuality: () => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      }
    }
    return null
  }
}

declare global {
  interface Window {
    va?: (event: string, data?: any) => void
  }
}
